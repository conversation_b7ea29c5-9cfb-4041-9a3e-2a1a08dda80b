name: renopay-production
region: lon

services:
  - name: web
    git:
      branch: main
      repo_clone_url: https://${REPO_USERNAME}:${REPO_PASSWORD}@github.com/${REPO_OWNER}/${REPO_NAME}.git

    build_command: npm run build
    run_command: npm start

    environment_slug: node-js
    instance_count: 1
    instance_size_slug: basic-xxs

    http_port: 3000

    envs:
      - key: NODE_ENV
        value: production

      - key: DATABASE_URL
        value: ${DATABASE_URL}
        type: SECRET

      - key: OPP_API_KEY
        value: ${OPP_API_KEY}
        type: SECRET

      - key: OPP_API_URL
        value: ${OPP_API_URL}

      - key: OPP_FILES_API_URL
        value: ${OPP_FILES_API_URL}

      - key: OPP_WEBHOOK_SECRET
        value: ${OPP_WEBHOOK_SECRET}
        type: SECRET

      - key: SITE_URL
        value: ${APP_URL}

      - key: SUPABASE_URL
        value: ${SUPABASE_URL}

      - key: SUPABASE_SECRET_KEY
        value: ${SUPABASE_SECRET_KEY}
        type: SECRET

      - key: POSTMARK_SERVER_API
        value: ${POSTMARK_SERVER_API}
        type: SECRET

      - key: RESEND_API_KEY
        value: ${RESEND_API_KEY}
        type: SECRET
