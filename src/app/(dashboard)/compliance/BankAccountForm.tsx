"use client";

import { createBankAccount } from "./actions";

export type BankAccountFormData = {
  account: {
    accountName: string;
    accountIban: string;
    accountBic: string;
  } | null;
};

type Props = {
  data: BankAccountFormData;
};

export default function BankAccountForm({ data }: Props) {
  const isDisabled = data.account !== null;

  return (
    <form action={createBankAccount}>
      <div style={{ maxWidth: "500px" }}>
        <h2>Bank Account</h2>

        <div style={{ marginBottom: "16px" }}>
          <label
            htmlFor="accountName"
            style={{ display: "block", marginBottom: "4px" }}
          >
            Account Name
          </label>
          <input
            type="text"
            id="accountName"
            name="accountName"
            defaultValue={data.account?.accountName ?? ""}
            disabled={isDisabled}
            required
            style={{
              width: "100%",
              padding: "8px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              backgroundColor: isDisabled ? "#f5f5f5" : "white",
            }}
          />
        </div>

        <div style={{ marginBottom: "16px" }}>
          <label
            htmlFor="accountIban"
            style={{ display: "block", marginBottom: "4px" }}
          >
            Account IBAN
          </label>
          <input
            type="text"
            id="accountIban"
            name="accountIban"
            defaultValue={data.account?.accountIban ?? ""}
            disabled={isDisabled}
            required
            pattern="[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}"
            maxLength={34}
            placeholder="**********************"
            style={{
              width: "100%",
              padding: "8px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              backgroundColor: isDisabled ? "#f5f5f5" : "white",
              textTransform: "uppercase",
            }}
          />
        </div>

        <div style={{ marginBottom: "16px" }}>
          <label
            htmlFor="accountBic"
            style={{ display: "block", marginBottom: "4px" }}
          >
            Account BIC
          </label>
          <input
            type="text"
            id="accountBic"
            name="accountBic"
            defaultValue={data.account?.accountBic ?? ""}
            disabled={isDisabled}
            required
            pattern="[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?"
            minLength={8}
            maxLength={11}
            placeholder="NWBKGB2L"
            style={{
              width: "100%",
              padding: "8px",
              border: "1px solid #ccc",
              borderRadius: "4px",
              backgroundColor: isDisabled ? "#f5f5f5" : "white",
              textTransform: "uppercase",
            }}
          />
        </div>

        {!isDisabled && (
          <button
            type="submit"
            style={{
              padding: "10px 20px",
              backgroundColor: "#0070f3",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontWeight: "600",
            }}
          >
            Create Bank Account
          </button>
        )}
      </div>
    </form>
  );
}
