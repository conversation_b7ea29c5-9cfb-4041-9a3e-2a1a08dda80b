"use client";

import { useState } from "react";

export type ChamberOfCommerceExtractFormData = {
  chamberOfCommerceExtractUploadLink: {
    allowedMimeTypes: string[];
    headers: Record<string, string>;
    maxFileSizeInBytes: number;
    url: string;
  } | null;
};

type Props = {
  data: ChamberOfCommerceExtractFormData;
};

function mimeTypeToExtension(mimeType: string): string {
  return "." + mimeType.split("/")[1] || "";
}

export default function ChamberOfCommerceForm({ data }: Props) {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  if (!data.chamberOfCommerceExtractUploadLink) {
    return null;
  }

  const { maxFileSizeInBytes: maxFileSize, allowedMimeTypes } =
    data.chamberOfCommerceExtractUploadLink;

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      setSelectedFile(null);
      setError(null);
      return;
    }

    // Validate file size
    if (file.size > maxFileSize) {
      const maxSizeMB = Math.round(maxFileSize / (1024 * 1024));
      setError(`File size must be less than ${maxSizeMB}MB`);
      setSelectedFile(null);
      return;
    }

    // Validate MIME type
    if (!allowedMimeTypes.includes(file.type)) {
      setError(`File type must be one of: ${allowedMimeTypes.join(", ")}`);
      setSelectedFile(null);
      return;
    }

    setSelectedFile(file);
    setError(null);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError("Please select a file");
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("file", selectedFile);

      const uploadResponse = await fetch(
        data.chamberOfCommerceExtractUploadLink!.url,
        {
          method: "POST",
          headers: data.chamberOfCommerceExtractUploadLink!.headers,
          body: formData,
        }
      );

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.error || "Upload failed");
      }

      setSuccess(true);
      setSelectedFile(null);
    } catch (error_) {
      setError(error_ instanceof Error ? error_.message : "Upload failed");
    } finally {
      setUploading(false);
    }
  };

  if (success) {
    return (
      <div style={{ maxWidth: "500px" }}>
        <h2>Chamber of Commerce Extract</h2>
        <div
          style={{
            padding: "16px",
            backgroundColor: "#d4edda",
            border: "1px solid #c3e6cb",
            borderRadius: "4px",
            color: "#155724",
          }}
        >
          File uploaded successfully! Your document is being verified.
        </div>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: "500px" }}>
      <h2>Chamber of Commerce Extract</h2>

      <div style={{ marginBottom: "16px" }}>
        <label
          htmlFor="cocExtract"
          style={{ display: "block", marginBottom: "4px" }}
        >
          Upload Chamber of Commerce Extract
        </label>
        <input
          type="file"
          id="cocExtract"
          accept={allowedMimeTypes.map(mimeTypeToExtension).join(",")}
          onChange={handleFileChange}
          disabled={uploading}
          style={{
            width: "100%",
            padding: "8px",
            border: "1px solid #ccc",
            borderRadius: "4px",
          }}
        />
        <small style={{ color: "#666", fontSize: "12px" }}>
          Allowed formats:{" "}
          {allowedMimeTypes.map(mimeTypeToExtension).join(", ")}. Max size:{" "}
          {Math.round(maxFileSize / (1024 * 1024))}MB
        </small>
      </div>

      {error && (
        <div
          style={{
            padding: "12px",
            backgroundColor: "#f8d7da",
            border: "1px solid #f5c6cb",
            borderRadius: "4px",
            color: "#721c24",
            marginBottom: "16px",
          }}
        >
          {error}
        </div>
      )}

      <button
        onClick={handleUpload}
        disabled={!selectedFile || uploading}
        style={{
          padding: "10px 20px",
          backgroundColor: !selectedFile || uploading ? "#ccc" : "#0070f3",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: !selectedFile || uploading ? "not-allowed" : "pointer",
          fontWeight: "600",
        }}
      >
        {uploading ? "Uploading..." : "Upload File"}
      </button>
    </div>
  );
}
