/* eslint-disable unicorn/no-nested-ternary */
import Link from "next/link";
import assert from "node:assert";

import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import {
  createServerActionSupabaseClient,
  oppQueryHandler,
  prismaClient,
} from "../../dependencies";
import BankAccountForm from "./BankAccountForm";
import ChamberOfCommerceForm from "./ChamberOfCommerceForm";

export const dynamic = "force-dynamic";

type Props = {
  searchParams: Promise<{ tab?: string }>;
};

export type GetMerchantComplianceRequirementsResponse = {
  bankAccount: ComplianceRequirementStatus;
  chamberOfCommerceExtract: ComplianceRequirementStatus;
  contact: ComplianceRequirementStatus;
  organizationStructure: ComplianceRequirementStatus;
  ultimateBenificialOwner: ComplianceRequirementStatus;
  other:
    | {
        status: "pending" | "unverified";
        url: string;
      }
    | {
        status: "verified";
      };
};

export type ComplianceRequirementStatus = "unverified" | "pending" | "verified";

export default async function CompliancePage({ searchParams }: Props) {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const member = await prismaClient.member.findFirstOrThrow({
    select: {
      organization: {
        select: {
          oppMerchantUid: true,
        },
      },
    },
    where: {
      userId: authUser.id,
    },
  });

  assert(
    member.organization.oppMerchantUid != null,
    "Organization should be connected to OPP merchant"
  );

  const requirements = await oppQueryHandler.getMerchantComplianceRequirements(
    member.organization.oppMerchantUid
  );

  const { tab = "bankAccount" } = await searchParams;
  const { other, ...primaryReqirements } = requirements;

  return (
    <div style={{ padding: "20px" }}>
      <h1>Compliance Requirements</h1>

      <div
        style={{
          display: "flex",
          gap: "8px",
          borderBottom: "2px solid #e5e7eb",
          marginBottom: "24px",
        }}
      >
        {Object.entries(primaryReqirements).map(([key, status]) => (
          <Link
            key={key}
            href={`/compliance?tab=${key}`}
            style={{
              padding: "12px 16px",
              textDecoration: "none",
              color: tab === key ? "#0070f3" : "#666",
              borderBottom:
                tab === key ? "2px solid #0070f3" : "2px solid transparent",
              fontWeight: tab === key ? "600" : "400",
              marginBottom: "-2px",
            }}
          >
            {status === "verified" ? "✅" : status === "pending" ? "⏳" : "❌"}{" "}
            {
              {
                bankAccount: "Bank Account",
                chamberOfCommerceExtract: "Chamber of Commerce Extract",
                contact: "Contact",
                organizationStructure: "Organization Structure",
                ultimateBenificialOwner: "Ultimate Benificial Owner",
              }[
                key as Exclude<
                  keyof GetMerchantComplianceRequirementsResponse,
                  "other"
                >
              ]
            }
          </Link>
        ))}
      </div>

      {other.status === "pending" || other.status === "unverified" ? (
        <Link
          href={other.url}
          style={{
            padding: "12px 16px",
            textDecoration: "none",
            color: "#666",
            borderBottom: "2px solid transparent",
            fontWeight: "400",
            marginBottom: "-2px",
          }}
        >
          {other.status === "pending" ? "⏳" : "❌"} Other
        </Link>
      ) : null}

      <div>
        {tab === "bankAccount" && (
          <BankAccountForm
            data={await oppQueryHandler.getBankAccountFormData(
              member.organization.oppMerchantUid
            )}
          />
        )}

        {tab === "chamberOfCommerceExtract" && (
          <ChamberOfCommerceForm
            data={await oppQueryHandler.getChamberOfCommerceExtractFormData(
              member.organization.oppMerchantUid
            )}
          />
        )}

        {tab === "contact" && <div>{/* Contact content will go here */}</div>}

        {tab === "organizationStructure" && (
          <div>{/* Organization Structure content will go here */}</div>
        )}

        {tab === "ultimateBenificialOwner" && (
          <div>{/* Ultimate Beneficial Owner content will go here */}</div>
        )}

        {tab === "other" && <div>{/* Other content will go here */}</div>}
      </div>
    </div>
  );
}
