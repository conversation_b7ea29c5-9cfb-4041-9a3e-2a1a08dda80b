"use server";

import { revalidatePath } from "next/cache";
import assert from "node:assert";

import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import {
  createServerActionSupabaseClient,
  oppQueryHandler,
  prismaClient,
} from "../../dependencies";

export async function createBankAccount(formData: FormData) {
  const accountName = formData.get("accountName") as string;
  const accountIban = formData.get("accountIban") as string;
  const accountBic = formData.get("accountBic") as string;

  assert(accountName, "Account name is required");
  assert(accountIban, "Account IBAN is required");
  assert(accountBic, "Account BIC is required");

  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const member = await prismaClient.member.findFirstOrThrow({
    select: {
      organization: {
        select: {
          oppMerchantUid: true,
        },
      },
    },
    where: {
      userId: authUser.id,
    },
  });

  assert(
    member.organization.oppMerchantUid != null,
    "Organization should be connected to OPP merchant"
  );

  await oppQueryHandler.createBankAccount({
    merchantUid: member.organization.oppMerchantUid,
    accountName,
    accountIban,
    accountBic,
  });

  revalidatePath("/compliance");
}
