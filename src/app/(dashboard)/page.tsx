import { Alert<PERSON>riangle } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  userRepository,
} from "@/app/dependencies";
import { ChartAreaInteractive } from "@/components/chart-area-interactive";
import { SectionCards } from "@/components/section-cards";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import PageLayout from "./PageLayout";

export const dynamic = "force-dynamic";

export default async function DashboardPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const user = await userRepository.findById(authUser.id);
  assert(user, "User not found");

  if (user.status === UserStatus.WAITING_ONBOARDING) {
    redirect("/onboarding");
  }

  return (
    <PageLayout
      heading={<h1 className="font-semibold">Dashboard</h1>}
      user={{
        email: user.email,
        name: user.name,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&size=128&background=random`,
      }}
    >
      <div className="px-4 lg:px-6">
        <Card className="border-yellow-500/50 bg-yellow-50/50 dark:bg-yellow-950/20">
          <CardHeader>
            <div className="flex items-start gap-3">
              <AlertTriangle className="mt-0.5 size-5 text-yellow-600 dark:text-yellow-500" />
              <div className="flex-1">
                <CardTitle className="text-base font-semibold text-yellow-900 dark:text-yellow-100">
                  KYC Verification Not Completed
                </CardTitle>
                <CardDescription className="mt-1.5 text-yellow-800 dark:text-yellow-200">
                  Please complete your KYC verification to access all features.{" "}
                  <Link
                    href="/compliance"
                    className="font-medium underline underline-offset-4 hover:no-underline"
                  >
                    Complete verification
                  </Link>
                </CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>
      <SectionCards />
      <div className="px-4 lg:px-6">
        <ChartAreaInteractive />
      </div>
    </PageLayout>
  );
}
