"use client";

import { Users } from "lucide-react";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import formatDate from "@/lib/utils/formatDate";

import { Organization, OrganizationStatus } from "./types";

export type OrganizationDetailsProps = {
  organization: Organization;
};

export default function OrganizationDetails({
  organization,
}: OrganizationDetailsProps) {
  return (
    <div className="px-4 space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{organization.name}</CardTitle>
              <CardDescription>
                Organization details and information
              </CardDescription>
            </div>
            <Button variant="outline" asChild>
              <Link href={`/organization/${organization.id}`}>
                <Users className="mr-2 h-4 w-4" />
                View Members
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Status
              </p>
              <Badge
                variant={
                  organization.status === OrganizationStatus.Active
                    ? ("default" as const)
                    : ("destructive" as const)
                }
                className="mt-1"
              >
                {organization.status}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Created At
              </p>
              <p className="text-sm mt-1">
                {formatDate(organization.createdAt)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
