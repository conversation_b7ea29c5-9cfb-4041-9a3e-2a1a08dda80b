import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  userRepository,
} from "@/app/dependencies";
import { But<PERSON> } from "@/components/ui/button";
import { UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import PageLayout from "../../PageLayout";
import { Project } from "../types";
import ProjectDetail from "./ProjectDetail";

export const dynamic = "force-dynamic";

type ProjectPageProps = {
  params: Promise<{ id: string }>;
};

// Mock data - replace with actual data fetching later
const mockProjects: Record<string, Project> = {
  "1": {
    id: "1",
    name: "Kitchen Renovation - 123 Main St",
    createdAt: new Date("2025-01-15").toISOString(),
    updatedAt: new Date("2025-01-20").toISOString(),
    milestones: [],
  },
  "2": {
    id: "2",
    name: "Bathroom Remodel - 456 Oak Ave",
    createdAt: new Date("2025-01-10").toISOString(),
    updatedAt: new Date("2025-01-18").toISOString(),
    milestones: [],
  },
  "3": {
    id: "3",
    name: "Full House Renovation - 789 Elm St",
    createdAt: new Date("2025-01-05").toISOString(),
    updatedAt: new Date("2025-01-22").toISOString(),
    milestones: [],
  },
};

export default async function ProjectPage({ params }: ProjectPageProps) {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const user = await userRepository.findById(authUser.id);
  assert(user, "User not found");

  if (user.status === UserStatus.WAITING_ONBOARDING) {
    redirect("/onboarding");
  }

  const { id } = await params;
  const project = mockProjects[id];

  if (!project) {
    notFound();
  }

  return (
    <PageLayout
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href="/projects">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Project Details</h1>
        </div>
      }
      user={{
        email: user.email,
        name: user.name,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&size=128&background=random`,
      }}
    >
      <ProjectDetail project={project} />
    </PageLayout>
  );
}
