"use client";

import { ReactNode, useActionState, useId, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

import { Milestone, MilestoneStatus } from "../../types";

export type NewMilestoneFormState = {
  error?: string;
  data?: Milestone;
};

export type NewMilestoneDialogProps = {
  projectId: string;
  milestone?: Milestone;
  onMilestoneCreate?: (milestone: Milestone) => void;
  onMilestoneUpdate?: (milestone: Milestone) => void;
  trigger: ReactNode;
};

function formatDateForInput(dateString: string): string {
  const date = new Date(dateString);
  return date.toISOString().split("T")[0];
}

export default function NewMilestoneDialog({
  projectId,
  milestone,
  onMilestoneCreate,
  onMilestoneUpdate,
  trigger,
}: NewMilestoneDialogProps): ReactNode {
  const isEditing = Boolean(milestone);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [state, formAction, isPending] = useActionState<
    NewMilestoneFormState,
    FormData
  >(async (prevState, formData) => {
    // Mock form action - replace with actual server action later
    const scope = formData.get("scope") as string;
    const startDate = formData.get("startDate") as string;
    const endDate = formData.get("endDate") as string;
    const budget = Number.parseFloat(formData.get("budget") as string);

    // Simulate success
    const result: NewMilestoneFormState = {
      data: {
        id: milestone?.id || Math.random().toString(36).slice(2, 11),
        scope,
        startDate: new Date(startDate).toISOString(),
        endDate: new Date(endDate).toISOString(),
        budget,
        status: milestone?.status || MilestoneStatus.UnderApproval,
      },
    };

    if (result.data) {
      if (isEditing && onMilestoneUpdate) {
        onMilestoneUpdate(result.data);
      } else if (!isEditing && onMilestoneCreate) {
        onMilestoneCreate(result.data);
      }
      setIsDialogOpen(false);
    }

    return result;
  }, {});

  const formId = useId();

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Milestone" : "Create New Milestone"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update milestone details"
              : "Define the scope, timeline, and budget for this milestone"}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <form id={formId} action={formAction}>
            <FieldGroup>
              {state.error && <FieldError>{state.error}</FieldError>}

              <input
                type="hidden"
                name="projectId"
                value={projectId}
                readOnly
              />
              {isEditing && milestone && (
                <input
                  type="hidden"
                  name="milestoneId"
                  value={milestone.id}
                  readOnly
                />
              )}

              <Field>
                <FieldLabel htmlFor="scope">Scope</FieldLabel>
                <Textarea
                  id="scope"
                  name="scope"
                  placeholder="Describe the work to be completed in this milestone..."
                  required
                  rows={4}
                  defaultValue={milestone?.scope}
                />
              </Field>

              <div className="grid grid-cols-2 gap-4">
                <Field>
                  <FieldLabel htmlFor="startDate">Start Date</FieldLabel>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    required
                    defaultValue={
                      milestone
                        ? formatDateForInput(milestone.startDate)
                        : undefined
                    }
                  />
                </Field>

                <Field>
                  <FieldLabel htmlFor="endDate">End Date</FieldLabel>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    required
                    defaultValue={
                      milestone
                        ? formatDateForInput(milestone.endDate)
                        : undefined
                    }
                  />
                </Field>
              </div>

              <Field>
                <FieldLabel htmlFor="budget">Budget (GBP)</FieldLabel>
                <Input
                  id="budget"
                  name="budget"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  required
                  defaultValue={milestone?.budget}
                />
              </Field>
            </FieldGroup>
          </form>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending && (isEditing ? "Saving..." : "Creating...")}
            {!isPending && (isEditing ? "Save Milestone" : "Create Milestone")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
