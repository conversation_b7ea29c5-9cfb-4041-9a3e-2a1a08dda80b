import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  userRepository,
} from "@/app/dependencies";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import PageLayout from "../../../../PageLayout";
import { Milestone, MilestoneStatus, Project } from "../../../types";
import MilestoneDetail from "./MilestoneDetail";

export const dynamic = "force-dynamic";

type MilestonePageProps = {
  params: Promise<{ id: string; milestoneId: string }>;
};

// Mock data - replace with actual data fetching later
const mockMilestones: Record<string, Milestone[]> = {
  "1": [
    {
      id: "m1",
      scope: "Demolition and site preparation",
      startDate: new Date("2025-02-01").toISOString(),
      endDate: new Date("2025-02-07").toISOString(),
      budget: 5000,
      status: MilestoneStatus.UnderApproval,
    },
    {
      id: "m2",
      scope: "Plumbing and electrical rough-in",
      startDate: new Date("2025-02-08").toISOString(),
      endDate: new Date("2025-02-14").toISOString(),
      budget: 8000,
      status: MilestoneStatus.Accepted,
    },
    {
      id: "m3",
      scope: "Installation of cabinets and countertops",
      startDate: new Date("2025-02-15").toISOString(),
      endDate: new Date("2025-02-21").toISOString(),
      budget: 12000,
      status: MilestoneStatus.UnderApproval,
    },
  ],
  "2": [
    {
      id: "m4",
      scope: "Remove old fixtures and tiles",
      startDate: new Date("2025-02-01").toISOString(),
      endDate: new Date("2025-02-05").toISOString(),
      budget: 3000,
      status: MilestoneStatus.Accepted,
    },
    {
      id: "m5",
      scope: "Install new fixtures and tiles",
      startDate: new Date("2025-02-06").toISOString(),
      endDate: new Date("2025-02-12").toISOString(),
      budget: 7000,
      status: MilestoneStatus.UnderApproval,
    },
  ],
  "3": [],
};

const mockProjects: Record<string, Project> = {
  "1": {
    id: "1",
    name: "Kitchen Renovation - 123 Main St",
    createdAt: new Date("2025-01-15").toISOString(),
    updatedAt: new Date("2025-01-20").toISOString(),
    milestones: [],
  },
  "2": {
    id: "2",
    name: "Bathroom Remodel - 456 Oak Ave",
    createdAt: new Date("2025-01-10").toISOString(),
    updatedAt: new Date("2025-01-18").toISOString(),
    milestones: [],
  },
  "3": {
    id: "3",
    name: "Full House Renovation - 789 Elm St",
    createdAt: new Date("2025-01-05").toISOString(),
    updatedAt: new Date("2025-01-22").toISOString(),
    milestones: [],
  },
};

export default async function MilestonePage({ params }: MilestonePageProps) {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const user = await userRepository.findById(authUser.id);
  assert(user, "User not found");

  if (user.status === UserStatus.WAITING_ONBOARDING) {
    redirect("/onboarding");
  }

  const { id: projectId, milestoneId } = await params;
  const project = mockProjects[projectId];

  if (!project) {
    notFound();
  }

  const milestones = mockMilestones[projectId] || [];
  const milestone = milestones.find((m) => m.id === milestoneId);

  if (!milestone) {
    notFound();
  }

  return (
    <PageLayout
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href={`/projects/${projectId}/milestones`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Milestone Details</h1>
        </div>
      }
      user={{
        email: user.email,
        name: user.name,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&size=128&background=random`,
      }}
    >
      <MilestoneDetail project={project} milestone={milestone} />
    </PageLayout>
  );
}
