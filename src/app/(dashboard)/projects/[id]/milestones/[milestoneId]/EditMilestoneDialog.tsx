"use client";

import { ReactNode, useId, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Field, FieldLabel } from "@/components/ui/field";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

import { Milestone } from "../../../types";

export type EditMilestoneDialogProps = {
  milestone: Milestone;
  trigger: ReactNode;
};

function formatDateForInput(dateString: string): string {
  const date = new Date(dateString);
  return date.toISOString().split("T")[0];
}

export default function EditMilestoneDialog({
  milestone,
  trigger,
}: EditMilestoneDialogProps): ReactNode {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const formId = useId();

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Milestone</DialogTitle>
          <DialogDescription asChild>
            <form id={formId}>
              <Field>
                <FieldLabel
                  htmlFor="scope"
                  style={{
                    display: "block",
                    marginBottom: "5px",
                    fontSize: "14px",
                  }}
                >
                  Scope
                </FieldLabel>
                <Textarea
                  id="scope"
                  name="scope"
                  required
                  rows={4}
                  defaultValue={milestone.scope}
                  placeholder="Describe the work to be completed in this milestone..."
                />
              </Field>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <Field>
                  <FieldLabel
                    htmlFor="startDate"
                    style={{
                      display: "block",
                      marginBottom: "5px",
                      fontSize: "14px",
                    }}
                  >
                    Start Date
                  </FieldLabel>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    required
                    defaultValue={formatDateForInput(milestone.startDate)}
                  />
                </Field>

                <Field>
                  <FieldLabel
                    htmlFor="endDate"
                    style={{
                      display: "block",
                      marginBottom: "5px",
                      fontSize: "14px",
                    }}
                  >
                    End Date
                  </FieldLabel>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    required
                    defaultValue={formatDateForInput(milestone.endDate)}
                  />
                </Field>
              </div>

              <Field className="mt-4">
                <FieldLabel
                  htmlFor="budget"
                  style={{
                    display: "block",
                    marginBottom: "5px",
                    fontSize: "14px",
                  }}
                >
                  Budget (GBP)
                </FieldLabel>
                <Input
                  id="budget"
                  name="budget"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  required
                  defaultValue={milestone.budget}
                />
              </Field>
            </form>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Close
            </Button>
          </DialogClose>
          <Button type="submit" form={formId}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
