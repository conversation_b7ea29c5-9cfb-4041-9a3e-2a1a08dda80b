"use client";

import { <PERSON>cil, Plus } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import formatDate from "@/lib/utils/formatDate";

import NewProjectDialog from "./NewProjectDialog";
import { Project } from "./types";

export type ProjectsListProps = {
  projects: Project[];
};

export default function ProjectsList({
  projects: initialProjects,
}: ProjectsListProps) {
  const [projects, setProjects] = useState(initialProjects);

  const handleProjectCreate = (newProject: { id: string; name: string }) => {
    const project: Project = {
      id: newProject.id,
      name: newProject.name,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      milestones: [],
    };
    setProjects((prev) => [project, ...prev]);
  };

  const handleProjectUpdate = (updatedProject: {
    id: string;
    name: string;
  }) => {
    setProjects((prev) =>
      prev.map((p) =>
        p.id === updatedProject.id
          ? {
              ...p,
              name: updatedProject.name,
              updatedAt: new Date().toISOString(),
            }
          : p
      )
    );
  };

  if (projects.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="mb-4 text-muted-foreground">No projects yet</p>
        <NewProjectDialog
          onProjectCreate={handleProjectCreate}
          trigger={
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create your first project
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="px-4 space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Projects</CardTitle>
              <CardDescription>
                Manage your renovation projects and milestones
              </CardDescription>
            </div>
            <NewProjectDialog
              onProjectCreate={handleProjectCreate}
              trigger={
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  New Project
                </Button>
              }
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow className="hover:bg-transparent border-b">
                  <TableHead className="h-12 px-4 font-medium">
                    Project Name
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Milestones
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Created At
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium">
                    Updated At
                  </TableHead>
                  <TableHead className="h-12 px-4 font-medium w-[100px]">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {projects.map((project) => (
                  <TableRow key={project.id} className="hover:bg-muted/50">
                    <TableCell className="h-16 px-4 font-medium">
                      <Link
                        href={`/projects/${project.id}`}
                        className="text-primary hover:underline"
                      >
                        {project.name}
                      </Link>
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {project.milestones.length}
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {formatDate(project.createdAt)}
                    </TableCell>
                    <TableCell className="h-16 px-4 text-sm text-muted-foreground">
                      {formatDate(project.updatedAt)}
                    </TableCell>
                    <TableCell className="h-16 px-4">
                      <TooltipProvider>
                        <Tooltip>
                          <NewProjectDialog
                            project={project}
                            onProjectUpdate={handleProjectUpdate}
                            trigger={
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  className="h-8 w-8"
                                >
                                  <Pencil className="size-4" />
                                </Button>
                              </TooltipTrigger>
                            }
                          />
                          <TooltipContent>Edit</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
