"use client";

import { ReactNode, useActionState, useId, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { Project } from "./types";

export type NewProjectFormState = {
  error?: string;
  data?: {
    id: string;
    name: string;
  };
};

export type NewProjectDialogProps = {
  project?: Project;
  onProjectCreate?: (project: { id: string; name: string }) => void;
  onProjectUpdate?: (project: { id: string; name: string }) => void;
  trigger?: ReactNode;
};

export default function NewProjectDialog({
  project,
  onProjectCreate,
  onProjectUpdate,
  trigger,
}: NewProjectDialogProps) {
  const isEditing = Boolean(project);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [state, formAction, isPending] = useActionState<
    NewProjectFormState,
    FormData
  >(async (prevState, formData) => {
    const name = formData.get("name") as string;

    const result: NewProjectFormState = {
      data: {
        id: project?.id || Math.random().toString(36).slice(2, 11),
        name,
      },
    };

    if (result.data) {
      if (isEditing && onProjectUpdate) {
        onProjectUpdate(result.data);
      } else if (!isEditing && onProjectCreate) {
        onProjectCreate(result.data);
      }
      setIsDialogOpen(false);
    }

    return result;
  }, {});

  const formId = useId();

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Project" : "Create New Project"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update project details"
              : "Enter project information to get started"}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <form id={formId} action={formAction}>
            <FieldGroup>
              {state.error && <FieldError>{state.error}</FieldError>}

              {isEditing && project && (
                <input type="hidden" name="id" value={project.id} readOnly />
              )}

              <Field>
                <FieldLabel htmlFor="name">Project Name</FieldLabel>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  placeholder="e.g., Kitchen Renovation - 123 Main St"
                  required
                  defaultValue={project?.name}
                />
              </Field>
            </FieldGroup>
          </form>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending && (isEditing ? "Saving..." : "Creating...")}
            {!isPending && (isEditing ? "Save Project" : "Create Project")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
