import {
  MemberRole,
  Organization as PrismaOrganization,
  OrganizationStatus as PrismaOrganizationStatus,
} from "@prisma/client";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  prismaClient,
  userRepository,
} from "@/app/dependencies";
import { Button } from "@/components/ui/button";
import { UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import OrganizationDetails from "../OrganizationDetails";
import PageLayout from "../PageLayout";
import { Organization as OrganizationType, OrganizationStatus } from "../types";

export const dynamic = "force-dynamic";

export default async function OrganizationPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const user = await userRepository.findById(authUser.id);
  assert(user, "User not found");

  if (user.status === UserStatus.WAITING_ONBOARDING) {
    redirect("/onboarding");
  }

  const org = await prismaClient.organization.findFirst({
    where: {
      members: {
        some: {
          userId: authUser.id,
          role: MemberRole.BUILDER_ADMIN,
        },
      },
    },
  });

  if (!org) {
    notFound();
  }

  return (
    <PageLayout
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Organization</h1>
        </div>
      }
      user={{
        email: user.email,
        name: user.name,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&size=128&background=random`,
      }}
    >
      <OrganizationDetails organization={parseOrganization(org)} />
    </PageLayout>
  );
}

function parseOrganization(organization: PrismaOrganization): OrganizationType {
  return {
    id: organization.id,
    name: organization.name,
    status: prismaOrganizationToOrganizationTypeMap[organization.status],
    createdAt: organization.createdAt.toISOString(),
  };
}

const prismaOrganizationToOrganizationTypeMap: Record<
  PrismaOrganizationStatus,
  OrganizationStatus
> = {
  [PrismaOrganizationStatus.ACTIVE]: OrganizationStatus.Active,
  [PrismaOrganizationStatus.SUSPENDED]: OrganizationStatus.Suspended,
};
