"use client";

import { Ban } from "lucide-react";
import { useActionState } from "react";

import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { suspendMember, SuspendMemberFormState } from "./actions";
import { Member } from "./types";

export type SuspendButtonProps = {
  member: Member;
  organizationId: string;
  onSuspend: (params: { userId: string; suspendedAt: string }) => void;
};

export default function SuspendButton({
  member,
  organizationId,
  onSuspend,
}: SuspendButtonProps) {
  const [, formAction, isPending] = useActionState<
    SuspendMemberFormState,
    FormData
  >(async (prevState, formData) => {
    const result = await suspendMember(prevState, formData);

    if (result.data) {
      onSuspend(result.data);
    }

    return result;
  }, {});

  return (
    <form action={formAction}>
      <input type="hidden" name="userId" value={member.userId} />
      <input type="hidden" name="organizationId" value={organizationId} />
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="submit"
              disabled={isPending}
              variant="outline"
              size="icon"
              className="h-8 w-8 text-destructive hover:bg-destructive hover:text-white"
            >
              {isPending ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <Ban className="size-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>Suspend</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </form>
  );
}
