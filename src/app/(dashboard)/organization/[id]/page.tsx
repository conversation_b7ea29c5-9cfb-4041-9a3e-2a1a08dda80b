import {
  MemberRole as PrismaMemberRole,
  MemberStatus as PrismaMemberStatus,
  Prisma,
  UserStatus,
} from "@prisma/client";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  organizationRepository,
  prismaClient,
  userRepository,
} from "@/app/dependencies";
import { Button } from "@/components/ui/button";
import {
  MemberRole as DomainMemberRole,
  MemberStatus as DomainMemberStatus,
} from "@/domain/organization";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import PageLayout from "../../PageLayout";
import OrganizationMembersList from "./OrganizationMembersList";
import { type Member, MemberRole, MemberStatus } from "./types";

type OrganizationPageProps = {
  params: Promise<{ id: string }>;
};

export default async function OrganizationPage({
  params,
}: OrganizationPageProps) {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const user = await userRepository.findById(authUser.id);
  assert(user, "User not found");

  if (user.status === UserStatus.WAITING_ONBOARDING) {
    redirect("/onboarding");
  }

  const { id } = await params;
  const org = await organizationRepository.findById(id);

  if (!org) {
    notFound();
  }

  const domainMember = org.members.find((m) => m.userId === authUser.id);
  assert(domainMember, "You are not a member of this organization");

  const prismaMembers = await prismaClient.member.findMany({
    where: { organizationId: id },
    include: {
      user: true,
    },
    orderBy: {
      invitedAt: "asc",
    },
  });

  const member: Member = {
    email: authUser.email,
    userId: domainMember.userId,
    role: domainMemberRoleToPrismaMemberRoleMap[domainMember.role],
    status: domainMemberStatusToDomainMemberStatusMap[domainMember.status],
    invitedAt: domainMember.invitedAt.toISOString(),
  };

  return (
    <PageLayout
      heading={
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
            <Link href="/organization">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-semibold">Organization Members</h1>
        </div>
      }
      user={{
        email: user.email,
        name: user.name,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&size=128&background=random`,
      }}
    >
      <OrganizationMembersList
        currentMember={member}
        organization={{ id, name: org.name }}
        members={prismaMembers.map(parsePrismaMemberWithUserToMemberRow)}
      />
    </PageLayout>
  );
}

function parsePrismaMemberWithUserToMemberRow(
  member: Prisma.MemberGetPayload<{
    include: {
      user: true;
    };
  }>
): Member {
  return {
    email: member.user.email,
    userId: member.userId,
    role: prismaMemberRoleToMemberRoleMap[member.role],
    status: prismaMemberStatusToMemberStatusMap[member.status],
    invitedAt: member.invitedAt.toISOString(),
    joinedAt: member.joinedAt?.toISOString(),
    suspendedAt: member.suspendedAt?.toISOString(),
  };
}

const prismaMemberRoleToMemberRoleMap: Record<PrismaMemberRole, MemberRole> = {
  [PrismaMemberRole.BUILDER_ADMIN]: MemberRole.BuilderAdmin,
  [PrismaMemberRole.BUILDER_USER]: MemberRole.BuilderUser,
};

const prismaMemberStatusToMemberStatusMap: Record<
  PrismaMemberStatus,
  MemberStatus
> = {
  [PrismaMemberStatus.ACTIVE]: MemberStatus.Active,
  [PrismaMemberStatus.INVITED]: MemberStatus.Invited,
  [PrismaMemberStatus.SUSPENDED]: MemberStatus.Suspended,
};

const domainMemberRoleToPrismaMemberRoleMap: Record<
  DomainMemberRole,
  MemberRole
> = {
  [DomainMemberRole.BUILDER_ADMIN]: MemberRole.BuilderAdmin,
  [DomainMemberRole.BUILDER_USER]: MemberRole.BuilderUser,
};

const domainMemberStatusToDomainMemberStatusMap: Record<
  DomainMemberStatus,
  MemberStatus
> = {
  [DomainMemberStatus.ACTIVE]: MemberStatus.Active,
  [DomainMemberStatus.INVITED]: MemberStatus.Invited,
  [DomainMemberStatus.SUSPENDED]: MemberStatus.Suspended,
};
