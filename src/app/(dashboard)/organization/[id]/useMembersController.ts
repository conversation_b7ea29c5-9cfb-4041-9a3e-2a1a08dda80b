import { type Member, MemberRole, MemberStatus } from "./types";

type InvateInput = {
  userId: string;
  email: string;
  invitedAt: string;
};

type SuspendInput = {
  userId: string;
  suspendedAt: string;
};

type ResumeInput = {
  userId: string;
  invitedAt: string;
};

type MemberController = {
  members: Member[];
  inviteMember: (input: InvateInput) => void;
  suspendMember: (input: SuspendInput) => void;
  resumeMember: (input: ResumeInput) => void;
};

export default function useMembersController({
  members,
  onChange,
}: {
  members: Member[];
  onChange: (
    updaterFnOrValue: Member[] | ((prevMembers: Member[]) => Member[])
  ) => void;
}): MemberController {
  return {
    members,
    inviteMember: (input: InvateInput): void => {
      onChange((prev) => {
        const newMember: Member = {
          userId: input.userId,
          email: input.email,
          invitedAt: input.invitedAt,
          status: MemberStatus.Invited,
          role: MemberRole.BuilderUser,
        };

        return [...prev, newMember];
      });
    },

    suspendMember: (input: SuspendInput): void => {
      onChange((prev) =>
        prev.map((m) =>
          m.userId === input.userId
            ? {
                ...m,
                status: MemberStatus.Suspended,
                suspendedAt: input.suspendedAt,
              }
            : m
        )
      );
    },

    resumeMember: (input: ResumeInput): void => {
      onChange((prev) =>
        prev.map((m) =>
          m.userId === input.userId
            ? { ...m, status: MemberStatus.Invited, invitedAt: input.invitedAt }
            : m
        )
      );
    },
  };
}
