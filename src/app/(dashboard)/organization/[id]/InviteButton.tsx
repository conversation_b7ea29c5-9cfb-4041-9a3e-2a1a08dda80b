"use client";

import { useActionState, useId, useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Field, FieldLabel } from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { inviteUser, InviteUserFormState } from "./actions";

export type InviteButtonProps = {
  organizationId: string;
  onInvite: (params: {
    userId: string;
    invitedAt: string;
    email: string;
  }) => void;
};

export default function InviteButton({
  organizationId,
  onInvite,
}: InviteButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const [state, formAction, isPending] = useActionState<
    InviteUserFormState,
    FormData
  >(async (prevState, formData) => {
    const result = await inviteUser(prevState, formData);

    if (result.data) {
      onInvite({
        userId: result.data.userId,
        invitedAt: result.data.invitedAt,
        email: formData.get("email") as string,
      });
      setIsDialogOpen(false);
    }

    return result;
  }, {});

  const formId = useId();

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button>Invite</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <DialogTitle>Invite Member</DialogTitle>
          <DialogDescription asChild>
            <form id={formId} action={formAction}>
              {state.error && (
                <div className="mb-4 text-destructive">{state.error}</div>
              )}

              <Input
                type="hidden"
                name="organizationId"
                value={organizationId}
                readOnly
              />

              <Field>
                <FieldLabel
                  htmlFor="email"
                  style={{
                    display: "block",
                    marginBottom: "5px",
                    fontSize: "14px",
                  }}
                >
                  Email Address
                </FieldLabel>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  required
                  placeholder="<EMAIL>"
                />
              </Field>
            </form>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <DialogClose asChild>
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsDialogOpen(false)}
            >
              Close
            </Button>
          </DialogClose>
          <Button type="submit" form={formId} disabled={isPending}>
            {isPending ? "Inviting..." : "Invite"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
