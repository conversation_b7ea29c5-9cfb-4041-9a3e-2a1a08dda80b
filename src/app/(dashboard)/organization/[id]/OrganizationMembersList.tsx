"use client";

import { useState } from "react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import InviteButton from "./InviteButton";
import MembersTable from "./MembersTable";
import { Member, MemberRole, Organization } from "./types";
import useMembersController from "./useMembersController";

export type OrganizationMembersListProps = {
  currentMember: Member;
  organization: Organization;
  members: Member[];
};

export default function OrganizationMembersList({
  currentMember,
  organization,
  members: initialMembers,
}: OrganizationMembersListProps) {
  const [members, setMembers] = useState<Member[]>(initialMembers);
  const controller = useMembersController({
    members,
    onChange: setMembers,
  });

  return (
    <div className="px-4 space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{organization.name} Members</CardTitle>
              <CardDescription>
                Manage organization members and their roles
              </CardDescription>
            </div>
            {currentMember.role === MemberRole.BuilderAdmin && (
              <InviteButton
                organizationId={organization.id}
                onInvite={controller.inviteMember}
              />
            )}
          </div>
        </CardHeader>
        <CardContent>
          <MembersTable
            currentMember={currentMember}
            organizationId={organization.id}
            members={members}
            onSuspend={controller.suspendMember}
            onResume={controller.resumeMember}
          />
        </CardContent>
      </Card>
    </div>
  );
}
