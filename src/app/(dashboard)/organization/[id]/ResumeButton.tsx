"use client";

import { RotateCcw } from "lucide-react";
import { useActionState } from "react";

import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { resumeMember, ResumeMemberFormState } from "./actions";
import { Member } from "./types";

export type ResumeButtonProps = {
  member: Member;
  organizationId: string;
  onResume: (params: { userId: string; invitedAt: string }) => void;
};

export default function ResumeButton({
  member,
  organizationId,
  onResume,
}: ResumeButtonProps) {
  const [, formAction, isPending] = useActionState<
    ResumeMemberFormState,
    FormData
  >(async (prevState, formData) => {
    const result = await resumeMember(prevState, formData);

    if (result.data) {
      onResume({ userId: member.userId, invitedAt: result.data.invitedAt });
    }

    return result;
  }, {});

  return (
    <form action={formAction}>
      <input type="hidden" name="userId" value={member.userId} />
      <input type="hidden" name="organizationId" value={organizationId} />
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="submit"
              disabled={isPending}
              variant="outline"
              size="icon"
              className="h-8 w-8"
            >
              {isPending ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              ) : (
                <RotateCcw className="size-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>Resume</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </form>
  );
}
