"use server";

import assert from "node:assert";

import {
  ACCEPT_INVITE_REDIRECT_URL,
  createServerActionSupabaseClient,
  emailService,
  organizationRepository,
  prismaClient,
  userRepository,
} from "@/app/dependencies";
import {
  invite<PERSON>eam<PERSON><PERSON>ber,
  Member,
  MemberRole,
  MemberStatus,
  resumeTeamMember,
  suspendTeamMember,
} from "@/domain/organization";
import { createUser, UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type InviteUserFormState = {
  data?: {
    userId: string;
    invitedAt: string;
  };
  error?: string;
};

export async function inviteUser(
  prevState: InviteUserFormState,
  formData: FormData
): Promise<InviteUserFormState> {
  const email = formData.get("email") as string | null;
  const orgId = formData.get("organizationId") as string | null;

  if (!email) {
    return { error: "Email is required" };
  }

  if (!orgId) {
    return { error: "Organization ID is required" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();

    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const org = await organizationRepository.findById(orgId);

    if (!org) {
      return { error: "Organization not found" };
    }

    const invitedBy = org.members.find((m) => m.userId === authUser.id);

    if (!invitedBy) {
      return {
        error: "You are not a member of this organization",
      };
    }

    const prismaUser = await prismaClient.user.findFirst({
      where: { email },
    });

    let invited: Member | undefined;

    if (!prismaUser) {
      const { error, data: invitedAuthUser } =
        await authService.sendInviteEmail({
          email,
          redirectTo: `${ACCEPT_INVITE_REDIRECT_URL}?organization_id=${org.id}&email=${encodeURIComponent(email)}`,
        });

      if (error) {
        return { error: error.message };
      }

      const user = createUser({
        id: invitedAuthUser.id,
        email,
        name: email,
        acceptedTermsAndConditionsAt: null,
        status: UserStatus.ACTIVE,
      });

      const newOrg = inviteTeamMember(org, {
        userId: invitedAuthUser.id,
        invitedBy,
        role: MemberRole.BUILDER_USER,
      });

      await userRepository.add(user);
      await organizationRepository.update(org, newOrg);

      invited = newOrg.members.find((m) => m.userId === user.id);
    } else {
      const newOrg = inviteTeamMember(org, {
        userId: prismaUser.id,
        invitedBy,
        role: MemberRole.BUILDER_USER,
      });

      await organizationRepository.update(org, newOrg);
      await emailService.sendTeamInviteEmail({
        to: prismaUser.email,
        redirectTo: `${ACCEPT_INVITE_REDIRECT_URL}?organization_id=${org.id}&email=${encodeURIComponent(prismaUser.email)}`,
      });

      invited = newOrg.members.find((m) => m.userId === prismaUser.id);
    }

    assert(invited, "Invited member not found");

    return {
      data: {
        userId: invited.userId,
        invitedAt: invited.invitedAt.toISOString(),
      },
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to invite member",
    };
  }
}

export type SuspendMemberFormState = {
  data?: {
    userId: string;
    suspendedAt: string;
  };
  error?: string;
};

export async function suspendMember(
  prevState: SuspendMemberFormState,
  formData: FormData
): Promise<SuspendMemberFormState> {
  const userId = formData.get("userId") as string | null;
  const orgId = formData.get("organizationId") as string | null;

  if (!userId) {
    return { error: "User ID is required" };
  }

  if (!orgId) {
    return { error: "Organization ID is required" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();

    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const org = await organizationRepository.findById(orgId);

    if (!org) {
      return { error: "Organization not found" };
    }

    const suspendedBy = org.members.find((m) => m.userId === authUser.id);

    if (!suspendedBy) {
      return { error: "You are not a member of this organization" };
    }

    const newOrg = suspendTeamMember(org, { userId, suspendedBy });
    await organizationRepository.update(org, newOrg);

    const suspendedMember = newOrg.members.find((m) => m.userId === userId);
    assert(suspendedMember, "Suspended member not found");
    assert(
      suspendedMember.status === MemberStatus.SUSPENDED,
      "Member is not suspended"
    );

    return {
      data: {
        userId: suspendedMember.userId,
        suspendedAt: suspendedMember.suspendedAt.toISOString(),
      },
    };
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "Failed to suspend member",
    };
  }
}

export type ResumeMemberFormState = {
  data?: {
    invitedAt: string;
  };
  error?: string;
};

export async function resumeMember(
  prevState: ResumeMemberFormState,
  formData: FormData
): Promise<ResumeMemberFormState> {
  const orgId = formData.get("organizationId") as string | null;
  const userId = formData.get("userId") as string | null;

  if (!orgId) {
    return { error: "Organization ID is required" };
  }

  if (!userId) {
    return { error: "User ID is required" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const authUser = await authService.getUser();

    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const resumingUser = await userRepository.findById(userId);

    if (!resumingUser) {
      return { error: "Resuming user not found" };
    }

    const org = await organizationRepository.findById(orgId);

    if (!org) {
      return { error: "Organization not found" };
    }

    const invitedBy = org.members.find((m) => m.userId === authUser.id);

    if (!invitedBy) {
      return {
        error: "You are not a member of this organization",
      };
    }

    const newOrg = resumeTeamMember(org, {
      userId: resumingUser.id,
      invitedBy,
    });
    await organizationRepository.update(org, newOrg);

    await emailService.sendTeamInviteEmail({
      to: resumingUser.email,
      redirectTo: `${ACCEPT_INVITE_REDIRECT_URL}?organization_id=${org.id}&email=${encodeURIComponent(resumingUser.email)}`,
    });

    const resumedMember = newOrg.members.find(
      (m) => m.userId === resumingUser.id
    );
    assert(resumedMember, "Resumed member not found");

    return {
      data: {
        invitedAt: resumedMember.invitedAt.toISOString(),
      },
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to resume member",
    };
  }
}
