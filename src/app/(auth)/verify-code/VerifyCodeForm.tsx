"use client";

import { useActionState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldDescription,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

import { verifyOtp, type VerifyOtpFormState } from "./actions";

export type VerifyRecoveryCodeFormProps = {
  email: string;
};

export default function VerifyRecoveryCodeForm({
  email,
}: VerifyRecoveryCodeFormProps) {
  const [state, formAction, isPending] = useActionState<
    VerifyOtpFormState,
    FormData
  >(verifyOtp, {});

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-xs">
        <Card>
          <CardHeader>
            <CardTitle>Enter verification code</CardTitle>
            <CardDescription>
              We sent a 6-digit code to your email.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={formAction}>
              <input type="hidden" name="email" value={email} />
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field>
                  <FieldLabel htmlFor="otp">Verification code</FieldLabel>
                  <InputOTP
                    maxLength={6}
                    id="otp"
                    name="token"
                    required
                    disabled={isPending}
                  >
                    <InputOTPGroup className="gap-2.5 *:data-[slot=input-otp-slot]:rounded-md *:data-[slot=input-otp-slot]:border">
                      <InputOTPSlot index={0} />
                      <InputOTPSlot index={1} />
                      <InputOTPSlot index={2} />
                      <InputOTPSlot index={3} />
                      <InputOTPSlot index={4} />
                      <InputOTPSlot index={5} />
                    </InputOTPGroup>
                  </InputOTP>
                  <FieldDescription>
                    Enter the 6-digit code sent to your email.
                  </FieldDescription>
                </Field>

                <FieldGroup>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Verifying..." : "Verify"}
                  </Button>
                  <FieldDescription className="text-center">
                    Check your email for the 6-digit verification code
                  </FieldDescription>
                </FieldGroup>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
