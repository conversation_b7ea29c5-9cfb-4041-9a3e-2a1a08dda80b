"use server";

import { redirect } from "next/navigation";

import { createServerActionSupabaseClient } from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type VerifyOtpFormState = {
  error?: string;
  success?: string;
};

export async function verifyOtp(
  prevState: VerifyOtpFormState,
  formData: FormData
): Promise<VerifyOtpFormState> {
  const email = formData.get("email") as string;
  const token = formData.get("token") as string;

  if (!email) {
    return {
      error: "Email is required",
    };
  }

  if (!token) {
    return {
      error: "Verification code is required",
    };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);
    const { error } = await authService.verifyOtp({
      email,
      token,
      type: "email",
    });

    if (error) {
      return { error: error.message };
    }
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }

  redirect("/");
}
