import { NextRequest, NextResponse } from "next/server";

import {
  createApiRouteSupabaseClient,
  userRepository,
} from "@/app/dependencies";
import { User } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const tokenHash = requestUrl.searchParams.get("token_hash");
  const redirectTo =
    requestUrl.searchParams.get("redirect_to") ?? "/reset-password";

  if (!tokenHash) {
    return NextResponse.redirect(
      new URL("/sign-in?error=invite_verification_failed", process.env.SITE_URL)
    );
  }

  const response = NextResponse.redirect(
    new URL(redirectTo, process.env.SITE_URL)
  );

  const failedResponse = NextResponse.redirect(
    new URL("/sign-in?error=invite_verification_failed", process.env.SITE_URL)
  );

  try {
    const supabase = await createApiRouteSupabaseClient(request, response);
    const authService = new SupabaseAuthService(supabase.auth);

    await authService.signOut();
    const { error, data: authUser } = await authService.verifyOtp({
      tokenHash,
      type: "invite",
    });

    if (error || !authUser) {
      return failedResponse;
    }

    const user = await userRepository.findById(authUser.id);

    if (!user) {
      return failedResponse;
    }

    if (!user.acceptedTermsAndConditionsAt) {
      const newUser: User = {
        ...user,
        acceptedTermsAndConditionsAt: new Date(),
      };

      await userRepository.update(user, newUser);
    }

    return response;
  } catch (error) {
    return NextResponse.redirect(
      new URL("/sign-in?error=invite_verification_failed", process.env.SITE_URL)
    );
  }
}
