"use client";

import { ReactNode, useActionState } from "react";

import { Organization } from "@/domain/organization";

import { acceptInvite, AcceptInviteFormState } from "./actions";

export type AcceptInviteFormProps = {
  organization: Organization;
};

export default function AcceptInviteForm({
  organization,
}: AcceptInviteFormProps): ReactNode {
  const [state, formAction, isPending] = useActionState<
    AcceptInviteFormState,
    FormData
  >(acceptInvite, {});

  return (
    <form
      action={formAction}
      style={{
        margin: "0 auto",
        padding: "20px",
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <input type="hidden" name="organizationId" value={organization.id} />

      {state.error && (
        <div style={{ marginBottom: "20px", color: "red" }}>{state.error}</div>
      )}

      <button
        type="submit"
        disabled={isPending}
        style={{
          width: "200px",
          padding: "10px",
          backgroundColor: isPending ? "#ccc" : "#007bff",
          color: "white",
          border: "none",
          borderRadius: "4px",
        }}
      >
        {isPending
          ? "Accepting invite..."
          : `Accept Invite to ${organization.name}`}
      </button>
    </form>
  );
}
