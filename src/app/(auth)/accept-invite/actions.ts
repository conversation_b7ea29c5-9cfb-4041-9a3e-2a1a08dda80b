"use server";

import { redirect, RedirectType } from "next/navigation";

import {
  createServerActionSupabaseClient,
  organizationRepository,
} from "@/app/dependencies";
import { acceptTeamInvite } from "@/domain/organization";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type AcceptInviteFormState = {
  error?: string;
};

export async function acceptInvite(
  prevState: AcceptInviteFormState,
  formData: FormData
): Promise<AcceptInviteFormState> {
  const organizationId = formData.get("organizationId") as string | null;

  if (!organizationId) {
    return { error: "Organization ID is required" };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);
    const authUser = await authService.getUser();

    if (!authUser) {
      return { error: "Unauthorized" };
    }

    const org = await organizationRepository.findById(organizationId);

    if (!org) {
      return { error: "Organization not found" };
    }

    const newOrg = acceptTeamInvite(org, { userId: authUser.id });
    await organizationRepository.update(org, newOrg);
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }

  redirect(`/organization/${organizationId}`, RedirectType.replace);
}
