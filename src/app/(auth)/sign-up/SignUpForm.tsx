"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useActionState } from "react";
import { useFormStatus } from "react-dom";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Field,
  FieldDescription,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { signUp, SignUpFormState } from "./actions";

function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button type="submit" disabled={pending} className="w-full">
      {pending ? "Signing up..." : "Sign Up"}
    </Button>
  );
}

export default function SignUpForm() {
  const searchParams = useSearchParams();
  const verificationError = searchParams.get("error") === "verification_failed";

  const initialState: SignUpFormState = verificationError
    ? { error: "Email verification failed. Please try again." }
    : {};

  const [state, formAction] = useActionState(signUp, initialState);

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Create an account</CardTitle>
            <CardDescription>
              Enter your email below to create your account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={formAction}>
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field>
                  <FieldLabel htmlFor="email">Email</FieldLabel>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    required
                    autoComplete="email"
                  />
                </Field>

                <Field>
                  <FieldLabel htmlFor="password">Password</FieldLabel>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    required
                    autoComplete="new-password"
                    minLength={6}
                  />
                </Field>

                <Field orientation="horizontal">
                  <Checkbox id="acceptedTerms" required name="acceptedTerms" />
                  <FieldLabel htmlFor="acceptedTerms">
                    I accept the Terms and Conditions
                  </FieldLabel>
                </Field>
                <Field>
                  <SubmitButton />
                  <FieldDescription className="text-center">
                    Already have an account?{" "}
                    <Link
                      href="/sign-in"
                      className="underline-offset-4 hover:underline"
                    >
                      Sign in
                    </Link>
                  </FieldDescription>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
