"use server";

import { redirect } from "next/navigation";

import {
  createServerActionSupabaseClient,
  EMAIL_VERIFICATION_REDIRECT_URL,
  userRepository,
} from "@/app/dependencies";
import { createUser, UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type SignUpFormState = {
  error?: string;
};

export async function signUp(
  prevState: SignUpFormState,
  formData: FormData
): Promise<SignUpFormState> {
  const email = formData.get("email") as string | null;
  const password = formData.get("password") as string | null;
  const acceptedTerms = formData.get("acceptedTerms") === "on";

  if (!email) {
    return {
      error: "Email is required",
    };
  }

  if (!password) {
    return {
      error: "Password is required",
    };
  }

  if (!acceptedTerms) {
    return {
      error: "You must accept the Terms and Conditions",
    };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { data: authUser, error } = await authService.signUp({
      email,
      password,
      redirectTo: EMAIL_VERIFICATION_REDIRECT_URL,
    });

    if (error) {
      return { error: error.message };
    }

    if (!authUser) {
      return { error: "Failed to create user" };
    }

    let user = await userRepository.findById(authUser.id);

    if (user) {
      return { error: "User already exists" };
    }

    user = createUser({
      id: authUser.id,
      email: authUser.email,
      acceptedTermsAndConditionsAt: new Date(),
      status: UserStatus.WAITING_ONBOARDING,
    });

    await userRepository.add(user);
  } catch (error) {
    return { error: "An unexpected error occurred" };
  }

  redirect(`/verify-email?email=${encodeURIComponent(email)}`);
}
