import { redirect } from "next/navigation";
import assert from "node:assert";

import {
  createServerActionSupabaseClient,
  userRepository,
} from "@/app/dependencies";
import { UserStatus } from "@/domain/user";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import OnboardingForm from "./OnboardingForm";

export const dynamic = "force-dynamic";

export default async function OnboardingPage() {
  const supabase = await createServerActionSupabaseClient();
  const authService = new SupabaseAuthService(supabase.auth);

  const authUser = await authService.getUser();
  assert(authUser, "Unauthorized");

  const user = await userRepository.findById(authUser.id);
  assert(user, "User not found");

  if (user.status === UserStatus.ACTIVE) {
    redirect("/");
  }

  return <OnboardingForm />;
}
