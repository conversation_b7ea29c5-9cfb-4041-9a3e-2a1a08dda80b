"use client";

import { useActionState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { type OnboardingFormState, submitOnboarding } from "./actions";

export default function OnboardingForm() {
  const [state, action, isPending] = useActionState<
    OnboardingFormState,
    FormData
  >(submitOnboarding, {});

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Complete Your Profile</CardTitle>
            <CardDescription>
              Please provide your name and company information to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={action}>
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field>
                  <FieldLabel htmlFor="name">Your Name</FieldLabel>
                  <Input
                    type="text"
                    id="name"
                    name="name"
                    required
                    disabled={isPending}
                    autoComplete="name"
                  />
                </Field>

                <Field>
                  <FieldLabel htmlFor="companyName">Company Name</FieldLabel>
                  <Input
                    type="text"
                    id="companyName"
                    name="companyName"
                    required
                    disabled={isPending}
                    autoComplete="organization"
                  />
                </Field>

                <Field>
                  <FieldLabel htmlFor="chamberOfCommerceNumber">
                    Chamber of Commerce Number
                  </FieldLabel>
                  <Input
                    type="text"
                    id="chamberOfCommerceNumber"
                    name="chamberOfCommerceNumber"
                    required
                    disabled={isPending}
                  />
                </Field>

                <Field>
                  <FieldLabel htmlFor="phoneNumber">Phone Number</FieldLabel>
                  <Input
                    type="tel"
                    id="phoneNumber"
                    name="phoneNumber"
                    required
                    disabled={isPending}
                    autoComplete="tel"
                  />
                </Field>

                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Completing..." : "Complete Onboarding"}
                  </Button>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
