"use server";

import { redirect } from "next/navigation";
import { randomUUID } from "node:crypto";

import {
  createServerActionSupabaseClient,
  oppQueryHandler,
  organizationRepository,
  userRepository,
} from "@/app/dependencies";
import {
  associateOrganizationWithOppMerchant,
  createOrganization,
} from "@/domain/organization";
import { finishOnboarding, UserStatus } from "@/domain/user";
import { OppApiError } from "@/opp/OppClient";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type OnboardingFormState = {
  error?: string;
};

export async function submitOnboarding(
  prevState: OnboardingFormState,
  formData: FormData
): Promise<OnboardingFormState> {
  const name = formData.get("name") as string;
  const companyName = formData.get("companyName") as string;
  const chamberOfCommerceNumber = formData.get(
    "chamberOfCommerceNumber"
  ) as string;
  const phoneNumber = formData.get("phoneNumber") as string;

  if (!name || name.trim().length === 0) {
    return { error: "Name is required" };
  }

  if (!companyName || companyName.trim().length === 0) {
    return { error: "Company name is required" };
  }

  if (!chamberOfCommerceNumber || chamberOfCommerceNumber.trim().length === 0) {
    return { error: "Chamber of Commerce Number is required" };
  }

  if (!phoneNumber || phoneNumber.trim().length === 0) {
    return { error: "Phone Number is required" };
  }

  const supabase = await createServerActionSupabaseClient();
  const authUser = await new SupabaseAuthService(supabase.auth).getUser();

  if (!authUser) {
    return { error: "Unauthorized" };
  }

  let user = await userRepository.findById(authUser.id);

  if (!user) {
    return { error: "User not found" };
  }

  const previousUser = user;

  if (user.status !== UserStatus.WAITING_ONBOARDING) {
    redirect("/");
  }

  user = finishOnboarding(user, { name: name.trim() });

  let organization = createOrganization({
    id: randomUUID(),
    name: companyName.trim(),
    adminUserId: user.id,
  });

  try {
    const oppMerchant = await oppQueryHandler.createMerchant({
      chamberOfCommerceNumber: chamberOfCommerceNumber.trim(),
      email: user.email,
      phoneNumber: phoneNumber.trim(),
    });

    organization = associateOrganizationWithOppMerchant(organization, {
      merchantId: oppMerchant.merchantUid,
      status: oppMerchant.merchantStatus,
      complianceStatus: oppMerchant.merchantComplianceStatus,
    });
  } catch (error) {
    if (error instanceof OppApiError) {
      return { error: `${error.statusCode} ${error.message}` };
    }

    return { error: String(error) };
  }

  await userRepository.update(previousUser, user);
  await organizationRepository.add(organization);

  redirect("/");
}
