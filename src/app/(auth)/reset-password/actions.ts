"use server";

import { redirect } from "next/navigation";

import { createServerActionSupabaseClient } from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type ResetPasswordFormState = {
  error?: string;
};

export async function resetPassword(
  prevState: ResetPasswordFormState,
  formData: FormData
): Promise<ResetPasswordFormState> {
  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    return {
      error: "Both password fields are required",
    };
  }

  if (password !== confirmPassword) {
    return {
      error: "Passwords do not match",
    };
  }

  if (password.length < 6) {
    return {
      error: "Password must be at least 6 characters",
    };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { error } = await authService.updateUser({
      password,
    });

    if (error) {
      return { error: error.message };
    }
  } catch (error) {
    return {
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }

  redirect("/");
}
