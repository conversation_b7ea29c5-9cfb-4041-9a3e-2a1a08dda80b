"use client";

import { useActionState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import { resetPassword, type ResetPasswordFormState } from "./actions";

export default function ResetPasswordForm() {
  const [state, action, isPending] = useActionState<
    ResetPasswordFormState,
    FormData
  >(resetPassword, {});

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Set New Password</CardTitle>
            <CardDescription>
              Enter your new password below to reset your password
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={action}>
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field>
                  <FieldLabel htmlFor="password">New Password</FieldLabel>
                  <Input
                    type="password"
                    id="password"
                    name="password"
                    required
                    disabled={isPending}
                    minLength={6}
                    autoComplete="new-password"
                  />
                </Field>

                <Field>
                  <FieldLabel htmlFor="confirmPassword">
                    Confirm New Password
                  </FieldLabel>
                  <Input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    required
                    disabled={isPending}
                    minLength={6}
                    autoComplete="new-password"
                  />
                </Field>

                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Resetting..." : "Reset Password"}
                  </Button>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
