"use client";

import Link from "next/link";
import { useActionState } from "react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Field,
  FieldDescription,
  FieldError,
  FieldGroup,
  FieldLabel,
} from "@/components/ui/field";
import { Input } from "@/components/ui/input";

import {
  type ForgotPasswordFormState,
  sendPasswordResetEmail,
} from "./actions";

export default function ForgotPasswordForm() {
  const [state, action, isPending] = useActionState<
    ForgotPasswordFormState,
    FormData
  >(sendPasswordResetEmail, {});

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm">
        <Card>
          <CardHeader>
            <CardTitle>Reset Password</CardTitle>
            <CardDescription>
              Enter your email address and we&apos;ll send you a link to reset
              your password
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form action={action}>
              <FieldGroup>
                {state.error && <FieldError>{state.error}</FieldError>}

                <Field>
                  <FieldLabel htmlFor="email">Email</FieldLabel>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="<EMAIL>"
                    required
                    disabled={isPending}
                    autoComplete="email"
                  />
                </Field>

                <Field>
                  <Button type="submit" disabled={isPending} className="w-full">
                    {isPending ? "Sending..." : "Send Reset Link"}
                  </Button>
                  <FieldDescription className="text-center">
                    <Link
                      href="/sign-in"
                      className="underline-offset-4 hover:underline"
                    >
                      Back to Sign In
                    </Link>
                  </FieldDescription>
                </Field>
              </FieldGroup>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
