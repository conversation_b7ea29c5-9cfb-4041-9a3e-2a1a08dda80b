"use server";

import { redirect } from "next/navigation";

import {
  createServerActionSupabaseClient,
  EMAIL_VERIFICATION_REDIRECT_URL,
} from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

export type ForgotPasswordFormState = {
  error?: string;
};

export async function sendPasswordResetEmail(
  prevState: ForgotPasswordFormState,
  formData: FormData
): Promise<ForgotPasswordFormState> {
  const email = formData.get("email") as string;

  if (!email) {
    return {
      error: "Email is required",
    };
  }

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { error } = await authService.resetPasswordForEmail({
      email,
      redirectTo: `${EMAIL_VERIFICATION_REDIRECT_URL}?type=recovery`,
    });

    if (error) {
      return { error: error.message };
    }
  } catch (error) {
    return { error: "An unexpected error occurred" };
  }

  redirect(`/verify-email?email=${encodeURIComponent(email)}`);
}
