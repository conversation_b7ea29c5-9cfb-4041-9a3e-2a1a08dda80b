import assert from "node:assert";

export type User =
  | {
      id: string;
      email: string;
      acceptedTermsAndConditionsAt: Date | null;
      status: UserStatus.WAITING_ONBOARDING;
    }
  | {
      id: string;
      email: string;
      name: string;
      onboardedAt: Date;
      acceptedTermsAndConditionsAt: Date | null;
      status: UserStatus.ACTIVE;
    };

export enum UserStatus {
  WAITING_ONBOARDING = "WAITING_ONBOARDING",
  ACTIVE = "ACTIVE",
}

type CreateUserParams =
  | {
      id: string;
      email: string;
      acceptedTermsAndConditionsAt: Date;
      status: UserStatus.WAITING_ONBOARDING;
    }
  | {
      id: string;
      email: string;
      name: string;
      acceptedTermsAndConditionsAt: Date | null;
      status: UserStatus.ACTIVE;
    };

export function createUser(params: CreateUserParams): User {
  if (params.status === UserStatus.WAITING_ONBOARDING) {
    return {
      id: params.id,
      email: params.email,
      acceptedTermsAndConditionsAt: params.acceptedTermsAndConditionsAt,
      status: UserStatus.WAITING_ONBOARDING,
    };
  }

  return {
    id: params.id,
    email: params.email,
    name: params.name,
    onboardedAt: new Date(),
    acceptedTermsAndConditionsAt: params.acceptedTermsAndConditionsAt,
    status: UserStatus.ACTIVE,
  };
}

export type FinishOnboardingParams = {
  name: string;
};

export function finishOnboarding(
  user: User,
  params: FinishOnboardingParams
): User {
  assert(
    user.status === UserStatus.WAITING_ONBOARDING,
    "User is already onboarded"
  );

  return {
    ...user,
    name: params.name,
    onboardedAt: new Date(),
    status: UserStatus.ACTIVE,
  };
}
