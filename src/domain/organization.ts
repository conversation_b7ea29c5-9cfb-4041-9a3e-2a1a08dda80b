import assert from "node:assert";

export type Organization = {
  id: string;
  members: Member[];
  name: string;
  status: OrganizationStatus;
  oppMerchant: {
    uid: string;
    status: OppMerchantStatus;
    complianceStatus: OppMerchantComplianceStatus;
  } | null;
};

export enum OrganizationStatus {
  ACTIVE = "ACTIVE",
  SUSPENDED = "SUSPENDED",
}

export type Member =
  | {
      userId: string;
      invitedBy: Member["userId"] | null;
      invitedAt: Date;
      role: MemberRole;
      status: MemberStatus.INVITED;
    }
  | {
      userId: string;
      invitedBy: Member["userId"] | null;
      invitedAt: Date;
      joinedAt: Date;
      role: MemberRole;
      status: MemberStatus.ACTIVE;
    }
  | {
      userId: string;
      invitedBy: Member["userId"] | null;
      invitedAt: Date;
      joinedAt: Date;
      suspendedAt: Date;
      suspendedBy: Member["userId"];
      role: MemberRole;
      status: MemberStatus.SUSPENDED;
    };

export enum MemberRole {
  BUILDER_ADMIN = "BUILDER_ADMIN",
  BUILDER_USER = "BUILDER_USER",
}

export enum MemberStatus {
  ACTIVE = "ACTIVE",
  INVITED = "INVITED",
  SUSPENDED = "SUSPENDED",
}

export enum OppMerchantStatus {
  NEW = "NEW",
  PENDING = "PENDING",
  LIVE = "LIVE",
  SUSPENDED = "SUSPENDED",
  TERMINATED = "TERMINATED",
  BLOCKED = "BLOCKED",
  DELETED = "DELETED",
}

export enum OppMerchantComplianceStatus {
  UNVERIFIED = "UNVERIFIED",
  PENDING = "PENDING",
  VERIFIED = "VERIFIED",
}

type CreateOrganizationParams = {
  id: string;
  name: string;
  adminUserId: string;
};

export function createOrganization(
  params: CreateOrganizationParams
): Organization {
  return {
    id: params.id,
    name: params.name,
    status: OrganizationStatus.ACTIVE,
    oppMerchant: null,
    members: [
      {
        userId: params.adminUserId,
        invitedBy: null,
        invitedAt: new Date(),
        joinedAt: new Date(),
        role: MemberRole.BUILDER_ADMIN,
        status: MemberStatus.ACTIVE,
      },
    ],
  };
}

type AssociateOrganizationWithOppMerchantParams = {
  merchantId: string;
  status: OppMerchantStatus;
  complianceStatus: OppMerchantComplianceStatus;
};

export function associateOrganizationWithOppMerchant(
  org: Organization,
  params: AssociateOrganizationWithOppMerchantParams
): Organization {
  return {
    ...org,
    oppMerchant: {
      uid: params.merchantId,
      status: params.status,
      complianceStatus: params.complianceStatus,
    },
  };
}

export function changeOppMerchantStatus(
  org: Organization,
  status: OppMerchantStatus
): Organization {
  assert(org.oppMerchant, `Expeccted oppMerchant to be defined: ${org.id}`);

  return {
    ...org,
    oppMerchant: {
      ...org.oppMerchant,
      status,
    },
  };
}

export function changeOppMerchantComplianceStatus(
  org: Organization,
  complianceStatus: OppMerchantComplianceStatus
): Organization {
  assert(org.oppMerchant, `Expeccted oppMerchant to be defined: ${org.id}`);

  return {
    ...org,
    oppMerchant: {
      ...org.oppMerchant,
      complianceStatus,
    },
  };
}

export function activateOrganization(org: Organization): Organization {
  return {
    ...org,
    status: OrganizationStatus.ACTIVE,
  };
}

export function suspendOrganization(org: Organization): Organization {
  return {
    ...org,
    status: OrganizationStatus.SUSPENDED,
  };
}

type InviteTeamMemberParams = {
  userId: string;
  invitedBy: Member;
  role: MemberRole;
};

export function inviteTeamMember(
  org: Organization,
  input: InviteTeamMemberParams
): Organization {
  const invitedBy = org.members.find(
    (m) => m.userId === input.invitedBy.userId
  );

  assert(invitedBy != null, "Inviter not found");
  assert(invitedBy.status === MemberStatus.ACTIVE, "Inviter is not active");
  assert(
    invitedBy.role === MemberRole.BUILDER_ADMIN,
    "Only admins can invite members"
  );

  const member = org.members.find((m) => m.userId === input.userId);
  assert(member == null, "Member already exists");

  const newMember: Member = {
    userId: input.userId,
    invitedBy: input.invitedBy.userId,
    invitedAt: new Date(),
    role: input.role,
    status: MemberStatus.INVITED,
  };

  return { ...org, members: [...org.members, newMember] };
}

type ResumeTeamMemberParams = {
  userId: string;
  invitedBy: Member;
};

export function resumeTeamMember(
  org: Organization,
  input: ResumeTeamMemberParams
): Organization {
  const invitedBy = org.members.find(
    (m) => m.userId === input.invitedBy.userId
  );

  assert(invitedBy != null, "Inviter not found");
  assert(invitedBy.status === MemberStatus.ACTIVE, "Inviter is not active");
  assert(
    invitedBy.role === MemberRole.BUILDER_ADMIN,
    "Only admins can invite members"
  );

  const member = org.members.find((m) => m.userId === input.userId);
  assert(member, "Member not found");

  return {
    ...org,
    members: org.members.map((m) =>
      m.userId === input.userId
        ? {
            ...m,
            status: MemberStatus.INVITED,
            invitedAt: new Date(),
          }
        : m
    ),
  };
}

type SuspendTeamMemberParams = {
  userId: string;
  suspendedBy: Member;
};

export function suspendTeamMember(
  org: Organization,
  input: SuspendTeamMemberParams
): Organization {
  const suspendedBy = org.members.find(
    (m) => m.userId === input.suspendedBy.userId
  );

  assert(suspendedBy != null, "Inviter not found");
  assert(suspendedBy.status === MemberStatus.ACTIVE, "Inviter is not active");
  assert(
    suspendedBy.role === MemberRole.BUILDER_ADMIN,
    "Only admins can invite members"
  );

  let member = org.members.find((m) => m.userId === input.userId);

  assert(member != null, "Member not found");
  assert(member.status === MemberStatus.ACTIVE, "Member is not active");

  member = {
    ...member,
    suspendedAt: new Date(),
    suspendedBy: input.suspendedBy.userId,
    status: MemberStatus.SUSPENDED,
  };

  return {
    ...org,
    members: org.members.map((m) => (m.userId === input.userId ? member : m)),
  };
}

type AcceptTeamInviteParams = {
  userId: string;
};

export function acceptTeamInvite(
  org: Organization,
  input: AcceptTeamInviteParams
): Organization {
  let member = org.members.find((m) => m.userId === input.userId);

  assert(member != null, "Member not found");
  assert(
    member.status === MemberStatus.INVITED,
    "Member has already accepted invite"
  );

  member = {
    ...member,
    joinedAt: new Date(),
    status: MemberStatus.ACTIVE,
  };

  return {
    ...org,
    members: org.members.map((m) => (m.userId === input.userId ? member : m)),
  };
}
