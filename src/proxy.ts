import { CookieMethodsServer } from "@supabase/ssr";
import { type NextRequest, NextResponse } from "next/server";

import { createSupabaseClient } from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

const publicPaths = ["/reset-password", "/invite-callback"];

const unauthenticatedOnlyPaths = [
  "/auth-callback",
  "/forgot-password",
  "/sign-in",
  "/sign-up",
  "/verify-email",
  "/verify-code",
];

const ON_AUTH_REDIRECT_COOKIE_KEY = "on_auth_redirect";

export async function proxy(request: NextRequest) {
  const path = request.nextUrl.pathname;
  const { response, client: supabase } = await updateSession(request);
  const isPublicPath = publicPaths.some((p) => path.startsWith(p));
  const isUnauthenticatedOnlyPath = unauthenticatedOnlyPaths.some((p) =>
    path.startsWith(p)
  );

  if (isPublicPath) {
    return response;
  }

  const authService = new SupabaseAuthService(supabase.auth);
  const isAuthenticated = await authService.isAuthenticated();

  if (isAuthenticated) {
    const redirectTo = request.cookies.get(ON_AUTH_REDIRECT_COOKIE_KEY)?.value;

    if (redirectTo) {
      const redirectResponse = redirectWithSession(
        response,
        new URL(redirectTo, process.env.SITE_URL)
      );
      redirectResponse.cookies.delete(ON_AUTH_REDIRECT_COOKIE_KEY);

      return redirectResponse;
    }

    return isUnauthenticatedOnlyPath
      ? redirectWithSession(response, new URL("/", process.env.SITE_URL))
      : response;
  }

  if (isUnauthenticatedOnlyPath) {
    return response;
  }

  const redirectResponse = NextResponse.redirect(
    new URL("/sign-in", process.env.SITE_URL)
  );

  redirectResponse.cookies.set(ON_AUTH_REDIRECT_COOKIE_KEY, request.url);
  return redirectResponse;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/ (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    "/((?!api/|_next/static|\\.well-known|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};

// Used to redirect to a new page with same supabase session
function redirectWithSession(
  response: NextResponse,
  redirectTo: URL
): NextResponse {
  const redirectResponse = NextResponse.redirect(redirectTo);

  response.cookies.getAll().forEach((cookie) => {
    redirectResponse.cookies.set(cookie.name, cookie.value, cookie);
  });

  return redirectResponse;
}

async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const cookieStorage: CookieMethodsServer = {
    getAll() {
      return request.cookies.getAll();
    },
    setAll(cookiesToSet) {
      cookiesToSet.forEach(({ name, value }) =>
        request.cookies.set(name, value)
      );
      supabaseResponse = NextResponse.next({
        request,
      });
      cookiesToSet.forEach(({ name, value, options }) =>
        supabaseResponse.cookies.set(name, value, options)
      );
    },
  };

  const supabase = createSupabaseClient(cookieStorage);

  await supabase.auth.getSession();

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return { response: supabaseResponse, client: supabase };
}
