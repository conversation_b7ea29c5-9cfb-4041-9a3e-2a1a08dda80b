import assert from "node:assert";

import type { BankAccountFormData } from "@/app/(dashboard)/compliance/BankAccountForm";
import type { ChamberOfCommerceExtractFormData } from "@/app/(dashboard)/compliance/ChamberOfCommerceForm";
import type { GetMerchantComplianceRequirementsResponse } from "@/app/(dashboard)/compliance/page";
import {
  OppMerchantComplianceStatus,
  OppMerchantStatus,
} from "@/domain/organization";

import OppClient, {
  GetObjectsResponse,
  PostMerchantResponse,
} from "./OppClient";

export type CreateMerchantParams = {
  email: string;
  chamberOfCommerceNumber: string;
  phoneNumber: string;
};

export type CreateMerchantResponse = {
  merchantUid: string;
  merchantStatus: OppMerchantStatus;
  merchantComplianceStatus: OppMerchantComplianceStatus;
};

export type CreateBankAccountParams = {
  merchantUid: string;
  accountName: string;
  accountIban: string;
  accountBic: string;
};

export default class OppQueryHandler {
  private readonly oppClient: OppClient;
  private readonly notifyUrl: string;
  private readonly returnUrl: string;

  constructor(options: {
    oppClient: OppClient;
    notifyUrl: string;
    returnUrl: string;
  }) {
    this.oppClient = options.oppClient;
    this.notifyUrl = options.notifyUrl;
    this.returnUrl = options.returnUrl;
  }

  async createMerchant(
    params: CreateMerchantParams
  ): Promise<CreateMerchantResponse> {
    const merchant = await this.oppClient.createMerchant({
      coc_nr: params.chamberOfCommerceNumber,
      country: "GBR",
      emailaddress: params.email,
      phone: params.phoneNumber,
      notify_url: this.notifyUrl,
      type: "business",
    });

    return {
      merchantUid: merchant.uid,
      merchantStatus:
        oppMerchantStatusToDomain[merchant.status] ?? OppMerchantStatus.NEW,
      merchantComplianceStatus:
        oppMerchantComplianceStatusToDomain[merchant.compliance.status] ??
        OppMerchantComplianceStatus.UNVERIFIED,
    };
  }

  async createBankAccount(params: CreateBankAccountParams): Promise<void> {
    await this.oppClient.createBankAccount(params.merchantUid, {
      account_name: params.accountName,
      account_bic: params.accountBic,
      account_iban: params.accountIban,
      notify_url: this.notifyUrl,
      return_url: this.returnUrl,
    });
  }

  async getMerchantStatus(merchantUid: string): Promise<OppMerchantStatus> {
    const rawMerchant = await this.oppClient.getMerchantByUid(merchantUid);

    return (
      oppMerchantStatusToDomain[rawMerchant.status] ?? OppMerchantStatus.NEW
    );
  }

  async getMerchantComplianceStatus(
    merchantUid: string
  ): Promise<OppMerchantComplianceStatus> {
    const merchant = await this.oppClient.getMerchantByUid(merchantUid);

    return (
      oppMerchantComplianceStatusToDomain[merchant.compliance.status] ??
      OppMerchantComplianceStatus.UNVERIFIED
    );
  }

  async getMerchantComplianceRequirements(
    merchantUid: string
  ): Promise<GetMerchantComplianceRequirementsResponse> {
    const merchant = await this.oppClient.getMerchantByUid(merchantUid);

    function chooseWorseComplianceRequirementStatus(
      a: PostMerchantResponse["compliance"]["requirements"][number]["status"],
      b: PostMerchantResponse["compliance"]["requirements"][number]["status"]
    ): PostMerchantResponse["compliance"]["requirements"][number]["status"] {
      return a === "unverified" || b === "unverified"
        ? "unverified"
        : // eslint-disable-next-line unicorn/no-nested-ternary
          a === "pending" || b === "pending"
          ? "pending"
          : "verified";
    }

    function collectWorseComplianceRequirementStatus(
      types: PostMerchantResponse["compliance"]["requirements"][number]["type"][]
    ): PostMerchantResponse["compliance"]["requirements"][number]["status"] {
      return merchant.compliance.requirements
        .filter((requirement) => types.includes(requirement.type))
        .map((requirement) => requirement.status)
        .reduce(chooseWorseComplianceRequirementStatus, "verified");
    }

    return {
      bankAccount: collectWorseComplianceRequirementStatus([
        "bank_account.required",
        "bank_account.verification.required",
      ]),
      chamberOfCommerceExtract: collectWorseComplianceRequirementStatus([
        "coc_extract.required",
      ]),
      contact: collectWorseComplianceRequirementStatus([
        "contact.phonenumber.required",
        "contact.phonenumber.verification.required",
        "contact.verification.required",
      ]),
      organizationStructure: collectWorseComplianceRequirementStatus([
        "organization_structure.required",
      ]),
      ultimateBenificialOwner: collectWorseComplianceRequirementStatus([
        "ubo.required",
        "ubo.verification.required",
      ]),
      other: {
        status: collectWorseComplianceRequirementStatus([
          "source_of_funds.required",
          "right_to_work.required",
        ]),
        url: merchant.compliance.overview_url,
      },
    };
  }

  async getBankAccountFormData(
    merchantUid: string
  ): Promise<BankAccountFormData> {
    const bankAccounts = await this.oppClient.getBankAccounts(merchantUid);
    const bankAccount = bankAccounts.data[0];

    if (!bankAccount) {
      return { account: null };
    }

    return {
      account: {
        accountName: bankAccount.account.account_name ?? "",
        accountIban: bankAccount.account.account_iban ?? "",
        accountBic: bankAccount.account.account_bic ?? "",
      },
    };
  }

  async getChamberOfCommerceExtractFormData(
    merchantUid: string
  ): Promise<ChamberOfCommerceExtractFormData> {
    const requirements = await this.oppClient.getMerchantByUid(merchantUid);
    const chamberOfCommerceExtractRequirement =
      requirements.compliance.requirements.find(
        (requirement) => requirement.type === "coc_extract.required"
      );

    if (
      !chamberOfCommerceExtractRequirement ||
      chamberOfCommerceExtractRequirement.status === "pending" ||
      chamberOfCommerceExtractRequirement.status === "verified"
    ) {
      return {
        chamberOfCommerceExtractUploadLink: null,
      };
    }

    const purpose = "coc_extract";
    const objects = await this.oppClient.getObjects();
    const fileSpec = this._findFileSpecByPurpose(objects, purpose);

    assert(
      fileSpec != null,
      `File purpose "${purpose}" not found in allowed objects`
    );

    const uploadLinkResult = await this.oppClient.createUpload({
      merchant_uid: merchantUid,
      purpose,
      object_uid: merchantUid,
    });

    return {
      chamberOfCommerceExtractUploadLink: {
        allowedMimeTypes: fileSpec.allowed_mime_types,
        headers: {
          "x-opp-files-token": uploadLinkResult.token,
        },
        maxFileSizeInBytes: this._parseFileSize(fileSpec.allowed_upload_size),
        url: uploadLinkResult.url,
      },
    };
  }

  private _findFileSpecByPurpose(
    allowedObjects: GetObjectsResponse,
    purpose: string
  ):
    | GetObjectsResponse["data"][number]["file_groups"][number]["files"][number]
    | null {
    for (const objectType of allowedObjects.data) {
      for (const fileGroup of objectType.file_groups) {
        for (const file of fileGroup.files) {
          if (file.purpose === purpose) {
            return file;
          }
        }
      }
    }
    return null;
  }

  private _parseFileSize(sizeString: string): number {
    // Parse sizes like "7M" to bytes
    const match = sizeString.match(/^(\d+)([KMG])$/);
    if (!match) {
      throw new Error(`Invalid file size format: ${sizeString}`);
    }

    const value = Number.parseInt(match[1], 10);
    const unit = match[2];

    const multipliers: Record<string, number> = {
      K: 1024,
      M: 1024 * 1024,
      G: 1024 * 1024 * 1024,
    };

    return value * multipliers[unit];
  }
}

const oppMerchantStatusToDomain: Record<
  PostMerchantResponse["status"],
  OppMerchantStatus
> = {
  new: OppMerchantStatus.NEW,
  pending: OppMerchantStatus.PENDING,
  live: OppMerchantStatus.LIVE,
  suspended: OppMerchantStatus.SUSPENDED,
  terminated: OppMerchantStatus.TERMINATED,
  blocked: OppMerchantStatus.BLOCKED,
  deleted: OppMerchantStatus.DELETED,
};

const oppMerchantComplianceStatusToDomain: Record<
  PostMerchantResponse["compliance"]["status"],
  OppMerchantComplianceStatus
> = {
  unverified: OppMerchantComplianceStatus.UNVERIFIED,
  pending: OppMerchantComplianceStatus.PENDING,
  verified: OppMerchantComplianceStatus.VERIFIED,
};
