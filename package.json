{"name": "renopay", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "next build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "prepare": "husky && DATABASE_URL=postgres://kek dotenvx run --ignore=MISSING_ENV_FILE -- prisma generate", "dev": "concurrently --raw 'next dev' 'wait-port 3000 && node scripts/port_tunnel.js'", "start": "next start", "test": "echo \"Error: no test specified\" && exit 1", "migrate:dev": "dotenvx run -- prisma migrate dev", "migrate:dev:reset": "dotenvx run -- prisma migrate reset"}, "author": "", "license": "ISC", "engines": {"node": "24.2.0", "npm": "11.3.0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@dotenvx/dotenvx": "^1.51.1", "@prisma/adapter-pg": "^7.1.0", "@prisma/client": "^7.1.0", "@radix-ui/react-avatar": "^1.1.11", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.8", "@radix-ui/react-slot": "^1.2.4", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@supabase/ssr": "^0.8.0", "@supabase/supabase-js": "^2.87.3", "@tabler/icons-react": "^3.36.0", "@tailwindcss/postcss": "^4.1.18", "@types/node": "24.10.0", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "concurrently": "^9.2.1", "eslint-kit": "^11.39.0", "husky": "^9.1.7", "input-otp": "^1.4.2", "lint-staged": "^16.2.6", "localtunnel": "^2.0.2", "lucide-react": "^0.562.0", "next": "^16.0.10", "next-themes": "^0.4.6", "postcss": "^8.5.6", "prettier": "^3.6.2", "prisma": "^7.1.0", "recharts": "^2.15.4", "resend": "^6.6.0", "sonner": "^2.0.7", "supabase": "^2.54.11", "tailwind-merge": "^3.4.0", "tailwindcss": "^4.1.18", "tw-animate-css": "^1.4.0", "typescript": "^5.9.3", "vaul": "^1.1.2", "wait-port": "^1.1.0"}}