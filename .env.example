# Database
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Application URL
SITE_URL=http://localhost:3000

# This one is only for local development. Will be sent automatically upon 'npm run dev'
LOCALTUNNEL_SUBDOMAIN=
LOCALTUNNEL_SITE_URL=

# Supabase - Local Development
# Get these by running: npx supabase status
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_SECRET_KEY=
SUPABASE_PROJECT_ID=

# Online Payment Platform (OPP)
OPP_API_KEY=<ask your tech lead to get it>
OPP_API_URL=https://api-sandbox.onlinebetaalplatform.nl/v1
OPP_FILES_API_URL=https://files-sandbox.onlinebetaalplatform.nl/v1
OPP_WEBHOOK_SECRET=<ask your tech lead to get it>

#Resend
RESEND_API_KEY=
